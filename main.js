const { app, BrowserWindow, ipcMain } = require('electron');
const path = require('path');

function createWindow() {
  const win = new BrowserWindow({
    width: 1200,
    height: 800,
    webPreferences: {
      nodeIntegration: true,
      contextIsolation: false
    },
    icon: path.join(__dirname, 'icon.ico'),
    title: 'نظام نقاط البيع والمحاسبة'
  });

  win.loadFile('index.html');
  
  // Open DevTools (remove for production)
  // win.webContents.openDevTools()
}

app.whenReady().then(() => {
  createWindow();
  
  app.on('activate', () => {
    if (BrowserWindow.getAllWindows().length === 0) {
      createWindow();
    }
  });
});

app.on('window-all-closed', () => {
  if (process.platform !== 'darwin') {
    app.quit();
  }
});

// Handle print requests from invoice windows
ipcMain.on('print-invoice', (event) => {
  const win = BrowserWindow.fromWebContents(event.sender);
  if (win) {
    win.webContents.print({}, (success, errorType) => {
      if (!success) {
        console.error('Print failed:', errorType);
      }
    });
  }
});