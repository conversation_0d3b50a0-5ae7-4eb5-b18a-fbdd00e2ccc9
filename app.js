// Initialize data stores
let products = JSON.parse(localStorage.getItem('products')) || [];
let categories = JSON.parse(localStorage.getItem('categories')) || ['مواد غذائية', 'مشروبات', 'منظفات', 'أدوات مكتبية'];
let sales = JSON.parse(localStorage.getItem('sales')) || [];
let customers = JSON.parse(localStorage.getItem('customers')) || [];
let expenses = JSON.parse(localStorage.getItem('expenses')) || [];
let users = JSON.parse(localStorage.getItem('users')) || [
    { id: 1, username: 'admin', password: 'admin123', type: 'admin', name: 'المدير العام' },
    { id: 2, username: 'branch_manager', password: 'manager123', type: 'branch_manager', name: 'مسؤول الفرع' }
];
let currentUser = JSON.parse(localStorage.getItem('currentUser')) || null;
let currency = localStorage.getItem('currency') || 'دينار'; // Default currency

// DOM Elements
const loginScreen = document.getElementById('login-screen');
const mainApp = document.getElementById('main-app');
const loginForm = document.getElementById('login-form');
const logoutBtn = document.getElementById('logout-btn');
const tabLinks = document.querySelectorAll('.nav-link');
const tabContent = document.getElementById('tab-content');

// Login functionality
loginForm.addEventListener('submit', (e) => {
    e.preventDefault();
    const username = document.getElementById('username').value;
    const password = document.getElementById('password').value;

    const user = users.find(u => u.username === username && u.password === password);

    if (user) {
        currentUser = user;
        localStorage.setItem('currentUser', JSON.stringify(currentUser));
        localStorage.setItem('loggedIn', 'true');

        loginScreen.style.display = 'none';
        mainApp.style.display = 'block';

        // Update header with user info
        updateUserInfo();

        // Load appropriate tabs based on user type
        loadTab('pos');
        updateNavigationForUser();
    } else {
        alert('اسم المستخدم أو كلمة المرور غير صحيحة');
    }
});

// Logout functionality
logoutBtn.addEventListener('click', () => {
    currentUser = null;
    localStorage.removeItem('currentUser');
    localStorage.removeItem('loggedIn');

    mainApp.style.display = 'none';
    loginScreen.style.display = 'block';
    document.getElementById('username').value = '';
    document.getElementById('password').value = '';
});

// Tab navigation
tabLinks.forEach(link => {
    link.addEventListener('click', (e) => {
        e.preventDefault();
        tabLinks.forEach(l => l.classList.remove('active'));
        e.target.classList.add('active');
        loadTab(e.target.dataset.tab);
    });
});

// Load tab content
function loadTab(tabName) {
    // Save current cart state before switching tabs
    saveData();

    switch(tabName) {
        case 'pos':
            loadPOS();
            break;
        case 'products':
            loadProducts();
            break;
        case 'categories':
            loadCategories();
            break;
        case 'customers':
            loadCustomers();
            break;
        case 'reports':
            loadReports();
            break;
        case 'expenses':
            loadExpenses();
            break;
        case 'settings':
            loadSettings();
            break;
        case 'users':
            loadUsers();
            break;
    }

    // Restore cart state after tab loads
    if (tabName === 'pos') {
        renderCart();
    }
}

// Update user info in header
function updateUserInfo() {
    if (currentUser) {
        const userInfo = document.createElement('div');
        userInfo.className = 'user-info';
        userInfo.innerHTML = `
            <span class="user-name">مرحباً، ${currentUser.name}</span>
            <span class="user-type badge ${currentUser.type === 'branch_manager' ? 'bg-success' : 'bg-primary'}">${getUserTypeText(currentUser.type)}</span>
        `;

        const header = document.querySelector('header');
        const existingUserInfo = header.querySelector('.user-info');
        if (existingUserInfo) {
            existingUserInfo.remove();
        }
        header.insertBefore(userInfo, header.querySelector('#logout-btn'));
    }
}

// Get user type text in Arabic
function getUserTypeText(type) {
    switch(type) {
        case 'admin': return 'مدير';
        case 'branch_manager': return 'مسؤول فرع';
        default: return 'مستخدم';
    }
}

// Update navigation based on user type
function updateNavigationForUser() {
    const nav = document.querySelector('.nav');

    // Remove existing users tab if exists
    const existingUsersTab = nav.querySelector('[data-tab="users"]');
    if (existingUsersTab) {
        existingUsersTab.parentElement.remove();
    }

    // Add users management tab only for branch managers
    if (currentUser && currentUser.type === 'branch_manager') {
        const usersTab = document.createElement('li');
        usersTab.className = 'nav-item';
        usersTab.innerHTML = '<a href="#" class="nav-link" data-tab="users">إدارة المستخدمين</a>';
        nav.appendChild(usersTab);

        // Add event listener for the new tab
        usersTab.querySelector('.nav-link').addEventListener('click', (e) => {
            e.preventDefault();
            document.querySelectorAll('.nav-link').forEach(l => l.classList.remove('active'));
            e.target.classList.add('active');
            loadTab('users');
        });
    }
}

// POS Functions
function loadPOS() {
    tabContent.innerHTML = `
        <div class="row">
            <div class="col-md-12">
                <h2 class="mb-3">نقاط البيع</h2>
                
                <div class="row mb-4">
                    <div class="col-md-6">
                        <label class="form-label">تصفية حسب التصنيف:</label>
                        <select id="category-filter" class="form-select">
                            <option value="all">جميع التصنيفات</option>
                            ${categories.map(cat => `<option value="${cat}">${cat}</option>`).join('')}
                        </select>
                    </div>
                    <div class="col-md-6">
                        <label class="form-label">بحث عن منتج:</label>
                        <input type="text" class="form-control" id="product-search" placeholder="ابحث باسم المنتج أو الباركود">
                    </div>
                </div>
                
                <div class="product-grid" id="product-grid">
                    ${renderProductsForPOS()}
                </div>
            </div>
        </div>
        <div class="fixed-cart">
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">سلة المشتريات</h3>
                </div>
                <div class="card-body">
                    <div id="cart"></div>
                </div>
                <div class="checkout-section">
                    <div class="mt-3">
                        <strong>الإجمالي: <span id="total-amount">0.00</span> ${currency}</strong>
                    </div>
                    <button id="checkout-btn" class="btn btn-success w-100 mt-3">إنهاء عملية البيع</button>
                </div>
            </div>
        </div>
    `;
    
    // Use event delegation for product cards
    document.getElementById('product-grid').addEventListener('click', (e) => {
        // Check if click occurred inside a product name button
        const button = e.target.closest('.product-card .product-name-btn');
        if (button) {
            const card = button.closest('.product-card');
            if (card && !card.classList.contains('out-of-stock')) {
                addToCart(card.dataset.id);
            }
        }
    });
    
    // Setup checkout button
    document.getElementById('checkout-btn').addEventListener('click', checkout);
    
    // Add event listener for search input
    if (document.getElementById('product-search')) {
        document.getElementById('product-search').addEventListener('input', () => {
            document.getElementById('product-grid').innerHTML = renderProductsForPOS();
        });
    }
    
    // Add event listener for category filter
    if (document.getElementById('category-filter')) {
        document.getElementById('category-filter').addEventListener('change', () => {
            document.getElementById('product-grid').innerHTML = renderProductsForPOS();
        });
    }
}

function renderProductsForPOS() {
    const category = document.getElementById('category-filter') ? document.getElementById('category-filter').value : 'all';
    const searchTerm = document.getElementById('product-search') ? document.getElementById('product-search').value.toLowerCase() : '';
    
    let filteredProducts = products;
    
    // Apply category filter
    if (category !== 'all') {
        filteredProducts = filteredProducts.filter(p => p.category === category);
    }
    
    // Apply search filter
    if (searchTerm) {
        filteredProducts = filteredProducts.filter(p =>
            p.name.toLowerCase().includes(searchTerm) ||
            (p.barcode && p.barcode.toLowerCase().includes(searchTerm))
        );
    }
    
    return filteredProducts.map(product => {
        const isOutOfStock = product.quantity <= 0;
        return `
            <div class="product-card ${isOutOfStock ? 'out-of-stock' : ''}" ${isOutOfStock ? '' : `data-id="${product.id}"`}>
                <button class="btn btn-outline-primary product-name-btn">
                    ${product.name}
                </button>
                <p>السعر: ${product.price} ${currency}</p>
                <p>${isOutOfStock ? '<span class="text-danger">تم النفاذ</span>' : `المتوفر: ${product.quantity}`}</p>
                <small class="text-muted">${product.category}</small>
                ${isOutOfStock ? '<div class="out-of-stock-alert">نفذت الكمية</div>' : ''}
            </div>
        `;
    }).join('');
}

function addToCart(productId) {
    const product = products.find(p => p.id == productId);
    if (!product) return;
    
    // Check if product is out of stock
    if (product.quantity <= 0) {
        alert('نفذت كمية هذا المنتج!');
        return;
    }
    
    // Find existing cart item
    const cartItem = sales.find(s => s.productId == productId && !s.completed);
    
    if (cartItem) {
        // Check if adding one more would exceed inventory
        if (cartItem.quantity + 1 > product.quantity) {
            alert(`لا يمكن إضافة أكثر من ${product.quantity} قطع من هذا المنتج`);
            return;
        }
        cartItem.quantity++;
    } else {
        sales.push({
            id: Date.now(),
            productId: product.id,
            name: product.name,
            price: product.price,
            quantity: 1,
            completed: false,
            timestamp: new Date()
        });
    }
    
    // Update cart display
    renderCart();
    saveData();
}

function renderCart() {
    const cart = document.getElementById('cart');
    const cartItems = sales.filter(sale => !sale.completed);
    
    if (cartItems.length === 0) {
        cart.innerHTML = '<p>السلة فارغة</p>';
        document.getElementById('total-amount').textContent = '0.00';
        return;
    }
    
    cart.innerHTML = cartItems.map(item => `
        <div class="cart-item">
            <div>
                <strong>${item.name}</strong>
                <p>${item.price} × ${item.quantity} = ${(item.price * item.quantity).toFixed(2)}</p>
            </div>
            <div>
                <button class="btn btn-sm btn-danger remove-item" data-id="${item.id}">حذف</button>
            </div>
        </div>
    `).join('');
    
    // Calculate total
    const total = cartItems.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    document.getElementById('total-amount').textContent = total.toFixed(2);
    
    // Use event delegation for remove buttons
    cart.addEventListener('click', (e) => {
        if (e.target.classList.contains('remove-item')) {
            const itemId = e.target.dataset.id;
            const index = sales.findIndex(s => s.id == itemId);
            if (index !== -1) {
                sales.splice(index, 1);
                renderCart();
                saveData();
            }
        }
    });
}

function checkout() {
    const cartItems = sales.filter(sale => !sale.completed);
    if (cartItems.length === 0) return;
    
    // Update inventory and mark sales as completed
    cartItems.forEach(item => {
        const product = products.find(p => p.id == item.productId);
        if (product) {
            product.quantity -= item.quantity;
        }
        item.completed = true;
    });
    
    // Generate invoice
    generateInvoice(cartItems);
    
    // Reset cart
    renderCart();
    saveData();
    alert('تمت عملية البيع بنجاح!');
}

function generateInvoice(items) {
    const total = items.reduce((sum, item) => sum + (item.price * item.quantity), 0);
    const invoiceContent = `
        <!DOCTYPE html>
        <html lang="ar" dir="rtl">
        <head>
            <meta charset="UTF-8">
            <title>فاتورة بيع</title>
            <style>
                body {
                    font-family: Arial, sans-serif;
                    padding: 20px;
                    max-width: 800px;
                    margin: 0 auto;
                }
                .invoice-header {
                    text-align: center;
                    margin-bottom: 20px;
                    border-bottom: 2px solid #333;
                    padding-bottom: 10px;
                }
                .invoice-date {
                    text-align: left;
                    margin-bottom: 30px;
                }
                .invoice-table {
                    width: 100%;
                    border-collapse: collapse;
                    margin-bottom: 30px;
                }
                .invoice-table th {
                    background-color: #f5f5f5;
                    border: 1px solid #ddd;
                    padding: 12px;
                    text-align: right;
                }
                .invoice-table td {
                    border: 1px solid #ddd;
                    padding: 10px;
                    text-align: right;
                }
                .invoice-total {
                    text-align: left;
                    font-size: 18px;
                    margin-top: 20px;
                    border-top: 2px solid #333;
                    padding-top: 10px;
                }
            </style>
            <script>
                window.onload = function() {
                    window.print();
                    setTimeout(function() {
                        window.close();
                    }, 1000);
                };
            <\/script>
        </head>
        <body>
            <h2 class="invoice-header">فاتورة بيع</h2>
            
            <p class="invoice-date">
                <strong>التاريخ:</strong> ${new Date().toLocaleString('ar-EG')}
            </p>
            
            <table class="invoice-table">
                <thead>
                    <tr>
                        <th>المنتج</th>
                        <th>السعر</th>
                        <th>الكمية</th>
                        <th>المجموع</th>
                    </tr>
                </thead>
                <tbody>
                    ${items.map(item => `
                        <tr>
                            <td>${item.name}</td>
                            <td>${item.price.toFixed(2)} ${currency}</td>
                            <td>${item.quantity}</td>
                            <td>${(item.price * item.quantity).toFixed(2)} ${currency}</td>
                        </tr>
                    `).join('')}
                </tbody>
            </table>
            
            <div class="invoice-total">
                <strong>الإجمالي:</strong> ${total.toFixed(2)} ${currency}
            </div>
        </body>
        </html>
    `;
    
    // Create Blob with proper MIME type
    const blob = new Blob([invoiceContent], { type: 'text/html; charset=utf-8' });
    const url = URL.createObjectURL(blob);
    
    // Open print window with object URL
    const printWindow = window.open(url, '_blank');
    
    // Clean up after printing
    printWindow.onload = function() {
        URL.revokeObjectURL(url);
    };
}

// Product Management
function loadProducts() {
    tabContent.innerHTML = `
        <div>
            <h2 class="mb-4">إدارة المنتجات</h2>
            
            <div class="card mb-4">
                <div class="card-header">
                    <h3 class="card-title">إضافة منتج جديد</h3>
                </div>
                <div class="card-body">
                    <form id="product-form">
                        <div class="mb-3">
                            <label class="form-label">اسم المنتج</label>
                            <input type="text" class="form-control" id="product-name" required>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">السعر</label>
                                <input type="number" step="0.01" class="form-control" id="product-price" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الكمية</label>
                                <input type="number" class="form-control" id="product-quantity" required>
                            </div>
                        </div>
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">التصنيف</label>
                                <select class="form-select" id="product-category">
                                    ${categories.map(cat => `<option value="${cat}">${cat}</option>`).join('')}
                                </select>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">الباركود (اختياري)</label>
                                <input type="text" class="form-control" id="product-barcode">
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary">حفظ المنتج</button>
                    </form>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">قائمة المنتجات</h3>
                </div>
                <div class="card-body">
                    <div id="product-list">
                        ${renderProductList()}
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Setup product form
    document.getElementById('product-form').addEventListener('submit', (e) => {
        e.preventDefault();
        addProduct();
    });
    
    // Use event delegation for product actions
    document.getElementById('product-list').addEventListener('click', (e) => {
        if (e.target.classList.contains('edit-product')) {
            const productId = e.target.dataset.id;
            // Edit functionality would go here
            alert(`تعديل المنتج ${productId} - هذه الميزة تحت التطوير`);
        } else if (e.target.classList.contains('delete-product')) {
            const productId = e.target.dataset.id;
            const index = products.findIndex(p => p.id == productId);
            if (index !== -1) {
                products.splice(index, 1);
                saveData();
                loadProducts();
            }
        }
    });
}

function renderProductList() {
    if (products.length === 0) return '<p>لا توجد منتجات</p>';
    
    return `
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>الاسم</th>
                    <th>السعر</th>
                    <th>الكمية</th>
                    <th>التصنيف</th>
                    <th>الباركود</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                ${products.map(product => `
                    <tr>
                        <td>${product.name}</td>
                        <td>${product.price} ${currency}</td>
                        <td>${product.quantity}</td>
                        <td>${product.category}</td>
                        <td>${product.barcode || '-'}</td>
                        <td>
                            <button class="btn btn-sm btn-warning edit-product" data-id="${product.id}">تعديل</button>
                            <button class="btn btn-sm btn-danger delete-product" data-id="${product.id}">حذف</button>
                        </td>
                    </tr>
                `).join('')}
            </tbody>
        </table>
    `;
}

function addProduct() {
    const newProduct = {
        id: Date.now(),
        name: document.getElementById('product-name').value,
        price: parseFloat(document.getElementById('product-price').value),
        quantity: parseInt(document.getElementById('product-quantity').value),
        category: document.getElementById('product-category').value,
        barcode: document.getElementById('product-barcode').value || null
    };
    
    products.push(newProduct);
    saveData();
    loadProducts(); // Refresh product list
}

// Category Management
function loadCategories() {
    tabContent.innerHTML = `
        <div>
            <h2 class="mb-4">إدارة التصنيفات</h2>
            
            <div class="card mb-4">
                <div class="card-header">
                    <h3 class="card-title">إضافة تصنيف جديد</h3>
                </div>
                <div class="card-body">
                    <form id="category-form">
                        <div class="mb-3">
                            <label class="form-label">اسم التصنيف</label>
                            <input type="text" class="form-control" id="category-name" required>
                        </div>
                        <button type="submit" class="btn btn-primary">حفظ التصنيف</button>
                    </form>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">قائمة التصنيفات</h3>
                </div>
                <div class="card-body">
                    <div id="category-list">
                        ${renderCategoryList()}
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Setup category form
    document.getElementById('category-form').addEventListener('submit', (e) => {
        e.preventDefault();
        addCategory();
    });
    // Setup event delegation for delete buttons
    document.getElementById('category-list').addEventListener('click', (e) => {
        if (e.target.classList.contains('delete-category')) {
            const index = e.target.dataset.index;
            if (index !== undefined) {
                // Check if any products use this category
                const categoryName = categories[index];
                const productsInCategory = products.filter(p => p.category === categoryName);
                
                if (productsInCategory.length > 0) {
                    alert(`لا يمكن حذف التصنيف "${categoryName}" لأنه مرتبط بمنتجات موجودة`);
                    return;
                }
                
                // Remove the category
                categories.splice(index, 1);
                saveData();
                
                // Update the category list
                document.getElementById('category-list').innerHTML = renderCategoryList();
                
                // Also update category dropdowns in other sections
                if (document.getElementById('product-category')) {
                    const categorySelect = document.getElementById('product-category');
                    categorySelect.innerHTML = categories.map(cat =>
                        `<option value="${cat}">${cat}</option>`
                    ).join('');
                }
            }
        }
    });
}

function renderCategoryList() {
    if (categories.length === 0) return '<p>لا توجد تصنيفات</p>';
    
    return `
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>اسم التصنيف</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                ${categories.map((category, index) => `
                    <tr>
                        <td>${category}</td>
                        <td>
                            <button class="btn btn-sm btn-danger delete-category" data-index="${index}">حذف</button>
                        </td>
                    </tr>
                `).join('')}
            </tbody>
        </table>
    `;
}

function addCategory() {
    const newCategory = document.getElementById('category-name').value.trim();
    if (newCategory && !categories.includes(newCategory)) {
        categories.push(newCategory);
        saveData();
        
        // Update the category list without reloading the entire tab
        document.getElementById('category-list').innerHTML = renderCategoryList();
        
        // Also update category dropdowns in other sections
        if (document.getElementById('product-category')) {
            const categorySelect = document.getElementById('product-category');
            categorySelect.innerHTML = categories.map(cat =>
                `<option value="${cat}">${cat}</option>`
            ).join('');
        }
        
        // Clear the input field after adding
        document.getElementById('category-name').value = '';
    }
}

// Add event delegation for category deletion
document.getElementById('category-list').addEventListener('click', (e) => {
    if (e.target.classList.contains('delete-category')) {
        const index = e.target.dataset.index;
        categories.splice(index, 1);
        saveData();
        loadCategories();
        
        // Update products that were in the deleted category
        products.forEach(product => {
            if (!categories.includes(product.category)) {
                product.category = categories[0] || 'عام';
            }
        });
        saveData();
    }
});

// Customer Management
function loadCustomers() {
    tabContent.innerHTML = `
        <div>
            <h2 class="mb-4">إدارة العملاء</h2>
            
            <div class="card mb-4">
                <div class="card-header">
                    <h3 class="card-title">إضافة عميل جديد</h3>
                </div>
                <div class="card-body">
                    <form id="customer-form">
                        <div class="row">
                            <div class="col-md-6 mb-3">
                                <label class="form-label">اسم العميل</label>
                                <input type="text" class="form-control" id="customer-name" required>
                            </div>
                            <div class="col-md-6 mb-3">
                                <label class="form-label">رقم الهاتف</label>
                                <input type="tel" class="form-control" id="customer-phone">
                            </div>
                        </div>
                        <button type="submit" class="btn btn-primary w-100 py-3">
                            <i class="bi bi-person-plus"></i> حفظ العميل
                        </button>
                    </form>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">قائمة العملاء</h3>
                </div>
                <div class="card-body">
                    <div id="customer-list">
                        ${renderCustomerList()}
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Setup customer form
    document.getElementById('customer-form').addEventListener('submit', (e) => {
        e.preventDefault();
        addCustomer();
    });
    
    // Setup event delegation for delete buttons
    document.getElementById('customer-list').addEventListener('click', (e) => {
        // Handle clicks on the delete button or its child elements
        const deleteBtn = e.target.closest('.delete-customer');
        if (deleteBtn) {
            const customerId = deleteBtn.dataset.id;
            const index = customers.findIndex(c => c.id == customerId);
            if (index !== -1) {
                customers.splice(index, 1);
                saveData();
                
                // Update the customer list without reloading the entire tab
                document.getElementById('customer-list').innerHTML = renderCustomerList();
            }
        }
    });
}

function renderCustomerList() {
    if (customers.length === 0) return '<p>لا يوجد عملاء</p>';
    
    return `
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>الاسم</th>
                    <th>رقم الهاتف</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                ${customers.map(customer => `
                    <tr>
                        <td>${customer.name}</td>
                        <td>${customer.phone || '-'}</td>
                        <td>
                            <button class="btn btn-sm btn-warning edit-customer me-2" data-id="${customer.id}">
                                <i class="bi bi-pencil"></i> تعديل
                            </button>
                            <button class="btn btn-sm btn-danger delete-customer" data-id="${customer.id}">
                                <i class="bi bi-trash"></i> حذف
                            </button>
                            </button>
                            <button class="btn btn-sm btn-danger delete-customer" data-id="${customer.id}">
                                <i class="bi bi-trash"></i> حذف
                            </button>
                        </td>
                    </tr>
                `).join('')}
            </tbody>
        </table>
    `;
}

function addCustomer() {
    const nameInput = document.getElementById('customer-name');
    const phoneInput = document.getElementById('customer-phone');
    
    // Validate inputs
    if (!nameInput.value.trim()) {
        alert('يرجى إدخال اسم العميل');
        return;
    }
    
    const newCustomer = {
        id: Date.now(),
        name: nameInput.value.trim(),
        phone: phoneInput.value.trim() || null
    };
    
    // Clear form
    nameInput.value = '';
    phoneInput.value = '';
    
    customers.push(newCustomer);
    saveData();
    loadCustomers(); // Refresh customer list
}

// Reports
function loadReports() {
    tabContent.innerHTML = `
        <div>
            <h2 class="mb-4">التقارير</h2>
            
            <div class="row mb-4">
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">مبيعات اليوم</h3>
                        </div>
                        <div class="card-body">
                            ${renderDailySales()}
                        </div>
                    </div>
                </div>
                <div class="col-md-6">
                    <div class="card">
                        <div class="card-header">
                            <h3 class="card-title">مبيعات الأسبوع</h3>
                        </div>
                        <div class="card-body">
                            ${renderWeeklySales()}
                        </div>
                    </div>
                </div>
            </div>
        </div>
    `;
}

function renderDailySales() {
    const today = new Date();
    today.setHours(0, 0, 0, 0);
    
    const dailySales = sales.filter(sale => 
        sale.completed && new Date(sale.timestamp) >= today
    );
    
    const total = dailySales.reduce((sum, sale) => sum + (sale.price * sale.quantity), 0);
    
    return `
        <p>عدد المعاملات: ${dailySales.length}</p>
        <p>إجمالي المبيعات: ${total.toFixed(2)} ${currency}</p>
    `;
}

function renderWeeklySales() {
    const today = new Date();
    today.setHours(0, 0, 0, 0); // Start of today
    const lastWeek = new Date(today);
    lastWeek.setDate(today.getDate() - 6); // Last 7 days including today
    
    const weeklySales = sales.filter(sale => {
        if (!sale.completed) return false;
        const saleDate = new Date(sale.timestamp);
        return saleDate >= lastWeek && saleDate <= today;
    });
    
    const total = weeklySales.reduce((sum, sale) => sum + (sale.price * sale.quantity), 0);
    
    return `
        <p>عدد المعاملات: ${weeklySales.length}</p>
        <p>إجمالي المبيعات: ${total.toFixed(2)} ${currency}</p>
    `;
}

// Expense Tracking
function loadExpenses() {
    tabContent.innerHTML = `
        <div>
            <h2 class="mb-4">تتبع المصروفات</h2>
            
            <div class="card mb-4">
                <div class="card-header">
                    <h3 class="card-title">إضافة مصروف جديد</h3>
                </div>
                <div class="card-body">
                    <form id="expense-form">
                        <div class="mb-3">
                            <label class="form-label">وصف المصروف</label>
                            <input type="text" class="form-control" id="expense-description" required>
                        </div>
                        <div class="mb-3">
                            <label class="form-label">المبلغ</label>
                            <input type="number" step="0.01" class="form-control" id="expense-amount" required>
                        </div>
                        <button type="submit" class="btn btn-primary">حفظ المصروف</button>
                    </form>
                </div>
            </div>
            
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">قائمة المصروفات</h3>
                </div>
                <div class="card-body">
                    <div id="expense-list">
                        ${renderExpenseList()}
                    </div>
                </div>
            </div>
        </div>
    `;
    
    // Setup expense form
    document.getElementById('expense-form').addEventListener('submit', (e) => {
        e.preventDefault();
        addExpense();
    });
}

function renderExpenseList() {
    if (expenses.length === 0) return '<p>لا توجد مصروفات</p>';
    
    return `
        <table class="table table-striped">
            <thead>
                <tr>
                    <th>الوصف</th>
                    <th>المبلغ</th>
                    <th>التاريخ</th>
                    <th>الإجراءات</th>
                </tr>
            </thead>
            <tbody>
                ${expenses.map(expense => `
                    <tr>
                        <td>${expense.description}</td>
                        <td>${expense.amount} ${currency}</td>
                        <td>${new Date(expense.timestamp).toLocaleDateString('ar-EG')}</td>
                        <td>
                            <button class="btn btn-sm btn-danger delete-expense" data-id="${expense.id}">حذف</button>
                        </td>
                    </tr>
                `).join('')}
            </tbody>
        </table>
    `;
}

function addExpense() {
    const newExpense = {
        id: Date.now(),
        description: document.getElementById('expense-description').value,
        amount: parseFloat(document.getElementById('expense-amount').value),
        timestamp: new Date()
    };
    
    expenses.push(newExpense);
    saveData();
    loadExpenses(); // Refresh expense list
}

// Users Management Functions
function loadUsers() {
    if (!currentUser || currentUser.type !== 'branch_manager') {
        tabContent.innerHTML = '<div class="alert alert-danger">ليس لديك صلاحية للوصول إلى هذه الصفحة</div>';
        return;
    }

    tabContent.innerHTML = `
        <div class="row">
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header">
                        <h3 class="card-title">إضافة مستخدم جديد</h3>
                    </div>
                    <div class="card-body">
                        <form id="user-form">
                            <div class="mb-3">
                                <label for="user-name" class="form-label">الاسم الكامل</label>
                                <input type="text" class="form-control" id="user-name" required>
                            </div>
                            <div class="mb-3">
                                <label for="user-username" class="form-label">اسم المستخدم</label>
                                <input type="text" class="form-control" id="user-username" required>
                            </div>
                            <div class="mb-3">
                                <label for="user-password" class="form-label">كلمة المرور</label>
                                <input type="password" class="form-control" id="user-password" required>
                            </div>
                            <div class="mb-3">
                                <label for="user-type" class="form-label">نوع المستخدم</label>
                                <select class="form-control" id="user-type" required>
                                    <option value="admin">مدير</option>
                                    <option value="branch_manager">مسؤول فرع</option>
                                </select>
                            </div>
                            <button type="submit" class="btn btn-primary">إضافة مستخدم</button>
                        </form>
                    </div>
                </div>
            </div>
            <div class="col-md-6">
                <div class="card">
                    <div class="card-header d-flex justify-content-between align-items-center">
                        <h3 class="card-title">قائمة المستخدمين</h3>
                        <div class="search-container">
                            <input type="text" id="user-search" class="form-control" placeholder="البحث عن مستخدم...">
                        </div>
                    </div>
                    <div class="card-body">
                        <div id="user-list">${renderUserList()}</div>
                    </div>
                </div>
            </div>
        </div>
    `;

    // Setup form submission
    document.getElementById('user-form').addEventListener('submit', (e) => {
        e.preventDefault();
        addUser();
    });

    // Setup search functionality
    document.getElementById('user-search').addEventListener('input', (e) => {
        const searchTerm = e.target.value.toLowerCase();
        const filteredUsers = users.filter(user =>
            user.name.toLowerCase().includes(searchTerm) ||
            user.username.toLowerCase().includes(searchTerm)
        );
        document.getElementById('user-list').innerHTML = renderUserList(filteredUsers);
    });
}

function renderUserList(userList = users) {
    if (userList.length === 0) {
        return '<p class="text-muted">لا توجد مستخدمين</p>';
    }

    return userList.map(user => `
        <div class="user-item border-bottom py-2">
            <div class="d-flex justify-content-between align-items-center">
                <div>
                    <strong>${user.name}</strong>
                    <br>
                    <small class="text-muted">@${user.username}</small>
                    <span class="badge ${user.type === 'branch_manager' ? 'bg-success' : 'bg-primary'} ms-2">
                        ${getUserTypeText(user.type)}
                    </span>
                </div>
                <div>
                    <button class="btn btn-sm btn-outline-primary edit-user me-2" data-id="${user.id}">
                        تعديل
                    </button>
                    <button class="btn btn-sm btn-outline-danger delete-user" data-id="${user.id}">
                        حذف
                    </button>
                </div>
            </div>
        </div>
    `).join('');
}

function addUser() {
    const name = document.getElementById('user-name').value.trim();
    const username = document.getElementById('user-username').value.trim();
    const password = document.getElementById('user-password').value;
    const type = document.getElementById('user-type').value;

    // Validate inputs
    if (!name || !username || !password) {
        alert('يرجى ملء جميع الحقول');
        return;
    }

    // Check if username already exists
    if (users.find(u => u.username === username)) {
        alert('اسم المستخدم موجود بالفعل');
        return;
    }

    const newUser = {
        id: Date.now(),
        name: name,
        username: username,
        password: password,
        type: type
    };

    users.push(newUser);
    saveData();

    // Clear form
    document.getElementById('user-form').reset();

    // Refresh user list
    document.getElementById('user-list').innerHTML = renderUserList();

    alert('تم إضافة المستخدم بنجاح');
}

// Add event delegation for user management buttons
document.addEventListener('click', (e) => {
    if (e.target.classList.contains('delete-user')) {
        const userId = parseInt(e.target.dataset.id);
        deleteUser(userId);
    } else if (e.target.classList.contains('edit-user')) {
        const userId = parseInt(e.target.dataset.id);
        editUser(userId);
    }
});

function deleteUser(userId) {
    const user = users.find(u => u.id === userId);
    if (!user) return;

    // Prevent deleting current user
    if (currentUser && currentUser.id === userId) {
        alert('لا يمكنك حذف حسابك الحالي');
        return;
    }

    if (confirm(`هل أنت متأكد من حذف المستخدم "${user.name}"؟`)) {
        const index = users.findIndex(u => u.id === userId);
        if (index !== -1) {
            users.splice(index, 1);
            saveData();

            // Refresh user list
            if (document.getElementById('user-list')) {
                document.getElementById('user-list').innerHTML = renderUserList();
            }

            alert('تم حذف المستخدم بنجاح');
        }
    }
}

function editUser(userId) {
    const user = users.find(u => u.id === userId);
    if (!user) return;

    // Create edit modal
    const modal = document.createElement('div');
    modal.className = 'modal fade show';
    modal.style.display = 'block';
    modal.style.backgroundColor = 'rgba(0,0,0,0.5)';
    modal.innerHTML = `
        <div class="modal-dialog">
            <div class="modal-content">
                <div class="modal-header">
                    <h5 class="modal-title">تعديل المستخدم</h5>
                    <button type="button" class="btn-close close-modal"></button>
                </div>
                <div class="modal-body">
                    <form id="edit-user-form">
                        <div class="mb-3">
                            <label for="edit-user-name" class="form-label">الاسم الكامل</label>
                            <input type="text" class="form-control" id="edit-user-name" value="${user.name}" required>
                        </div>
                        <div class="mb-3">
                            <label for="edit-user-username" class="form-label">اسم المستخدم</label>
                            <input type="text" class="form-control" id="edit-user-username" value="${user.username}" required>
                        </div>
                        <div class="mb-3">
                            <label for="edit-user-password" class="form-label">كلمة المرور الجديدة (اتركها فارغة للاحتفاظ بالحالية)</label>
                            <input type="password" class="form-control" id="edit-user-password">
                        </div>
                        <div class="mb-3">
                            <label for="edit-user-type" class="form-label">نوع المستخدم</label>
                            <select class="form-control" id="edit-user-type" required>
                                <option value="admin" ${user.type === 'admin' ? 'selected' : ''}>مدير</option>
                                <option value="branch_manager" ${user.type === 'branch_manager' ? 'selected' : ''}>مسؤول فرع</option>
                            </select>
                        </div>
                    </form>
                </div>
                <div class="modal-footer">
                    <button type="button" class="btn btn-secondary close-modal">إلغاء</button>
                    <button type="button" class="btn btn-primary save-user-changes" data-id="${userId}">حفظ التغييرات</button>
                </div>
            </div>
        </div>
    `;

    document.body.appendChild(modal);

    // Handle modal close
    modal.querySelectorAll('.close-modal').forEach(btn => {
        btn.addEventListener('click', () => {
            modal.remove();
        });
    });

    // Handle save changes
    modal.querySelector('.save-user-changes').addEventListener('click', () => {
        saveUserChanges(userId, modal);
    });
}

function saveUserChanges(userId, modal) {
    const user = users.find(u => u.id === userId);
    if (!user) return;

    const name = modal.querySelector('#edit-user-name').value.trim();
    const username = modal.querySelector('#edit-user-username').value.trim();
    const password = modal.querySelector('#edit-user-password').value;
    const type = modal.querySelector('#edit-user-type').value;

    // Validate inputs
    if (!name || !username) {
        alert('يرجى ملء جميع الحقول المطلوبة');
        return;
    }

    // Check if username already exists (excluding current user)
    if (users.find(u => u.username === username && u.id !== userId)) {
        alert('اسم المستخدم موجود بالفعل');
        return;
    }

    // Update user data
    user.name = name;
    user.username = username;
    if (password) {
        user.password = password;
    }
    user.type = type;

    // Update current user if editing self
    if (currentUser && currentUser.id === userId) {
        currentUser = user;
        localStorage.setItem('currentUser', JSON.stringify(currentUser));
        updateUserInfo();
    }

    saveData();

    // Refresh user list
    if (document.getElementById('user-list')) {
        document.getElementById('user-list').innerHTML = renderUserList();
    }

    modal.remove();
    alert('تم تحديث بيانات المستخدم بنجاح');
}

// Save all data to localStorage
function saveData() {
    localStorage.setItem('products', JSON.stringify(products));
    localStorage.setItem('categories', JSON.stringify(categories));
    localStorage.setItem('sales', JSON.stringify(sales));
    localStorage.setItem('customers', JSON.stringify(customers));
    localStorage.setItem('expenses', JSON.stringify(expenses));
    localStorage.setItem('users', JSON.stringify(users));
}

// Settings Management
function loadSettings() {
    tabContent.innerHTML = `
        <div>
            <h2 class="mb-4">الإعدادات</h2>
            
            <div class="card">
                <div class="card-header">
                    <h3 class="card-title">إعدادات النظام</h3>
                </div>
                <div class="card-body">
                    <form id="settings-form">
                        <div class="mb-3">
                            <label class="form-label">العملة</label>
                            <select class="form-select" id="currency-select">
                                <option value="دينار" ${currency === 'دينار' ? 'selected' : ''}>دينار</option>
                                <option value="دولار" ${currency === 'دولار' ? 'selected' : ''}>دولار</option>
                                <option value="يورو" ${currency === 'يورو' ? 'selected' : ''}>يورو</option>
                                <option value="ريال" ${currency === 'ريال' ? 'selected' : ''}>ريال</option>
                                <option value="الأوقية" ${currency === 'الأوقية' ? 'selected' : ''}>الأوقية</option>
                                <option value="الجنيه" ${currency === 'الجنيه' ? 'selected' : ''}>الجنيه</option>
                                <option value="الدرهم" ${currency === 'الدرهم' ? 'selected' : ''}>الدرهم</option>
                                <option value="الشلن" ${currency === 'الشلن' ? 'selected' : ''}>الشلن</option>
                                <option value="الفرنك" ${currency === 'الفرنك' ? 'selected' : ''}>الفرنك</option>
                                <option value="الليرة" ${currency === 'الليرة' ? 'selected' : ''}>الليرة</option>
                                <!-- New currencies added -->
                                <option value="جنيه مصري" ${currency === 'جنيه مصري' ? 'selected' : ''}>جنيه مصري</option>
                                <option value="ريال سعودي" ${currency === 'ريال سعودي' ? 'selected' : ''}>ريال سعودي</option>
                                <option value="درهم إماراتي" ${currency === 'درهم إماراتي' ? 'selected' : ''}>درهم إماراتي</option>
                                <option value="دينار كويتي" ${currency === 'دينار كويتي' ? 'selected' : ''}>دينار كويتي</option>
                                <option value="دينار بحريني" ${currency === 'دينار بحريني' ? 'selected' : ''}>دينار بحريني</option>
                                <option value="ليرة لبنانية" ${currency === 'ليرة لبنانية' ? 'selected' : ''}>ليرة لبنانية</option>
                                <option value="ليرة سورية" ${currency === 'ليرة سورية' ? 'selected' : ''}>ليرة سورية</option>
                                <option value="ريال قطري" ${currency === 'ريال قطري' ? 'selected' : ''}>ريال قطري</option>
                                <option value="ريال عماني" ${currency === 'ريال عماني' ? 'selected' : ''}>ريال عماني</option>
                                <option value="دينار جزائري" ${currency === 'دينار جزائري' ? 'selected' : ''}>دينار جزائري</option>
                                <option value="دينار تونسي" ${currency === 'دينار تونسي' ? 'selected' : ''}>دينار تونسي</option>
                                <option value="درهم مغربي" ${currency === 'درهم مغربي' ? 'selected' : ''}>درهم مغربي</option>
                            </select>
                        </div>
                        <button type="submit" class="btn btn-primary">حفظ الإعدادات</button>
                    </form>
                </div>
            </div>
        </div>
    `;
    
    document.getElementById('settings-form').addEventListener('submit', (e) => {
        e.preventDefault();
        const newCurrency = document.getElementById('currency-select').value;
        currency = newCurrency;
        localStorage.setItem('currency', currency);
        alert('تم حفظ الإعدادات بنجاح');
// Add reset button and functionality
    const resetSection = document.createElement('div');
    resetSection.className = 'mt-4';
    resetSection.innerHTML = `
        <div class="card">
            <div class="card-header">
                <h3 class="card-title">إعادة تعيين النظام</h3>
            </div>
            <div class="card-body">
                <p class="text-danger">تحذير: هذه العملية ستحذف جميع البيانات ولا يمكن التراجع عنها!</p>
                <button id="reset-btn" class="btn btn-danger w-100">إعادة تعيين كاملة</button>
            </div>
        </div>
    `;
    
    // Add to settings container
    const settingsContainer = document.querySelector('#settings-form').parentElement;
    settingsContainer.parentElement.appendChild(resetSection);
    
    // Attach reset functionality
    document.getElementById('reset-btn').addEventListener('click', () => {
        if (confirm('هل أنت متأكد أنك تريد حذف جميع البيانات؟ لا يمكن التراجع عن هذه العملية.')) {
            localStorage.clear();
            // Reset in-memory data
            products = [];
            categories = ['مواد غذائية', 'مشروبات', 'منظفات', 'أدوات مكتبية'];
            sales = [];
            customers = [];
            expenses = [];
            users = [
                { id: 1, username: 'admin', password: 'admin123', type: 'admin', name: 'المدير العام' },
                { id: 2, username: 'branch_manager', password: 'manager123', type: 'branch_manager', name: 'مسؤول الفرع' }
            ];
            currentUser = null;
            currency = 'دينار';
            
            alert('تم حذف جميع البيانات بنجاح. سيتم إعادة تحميل الصفحة.');
            location.reload();
        }
    });
    });
// Add reset functionality
    const resetBtn = document.createElement('button');
    resetBtn.id = 'reset-btn';
    resetBtn.className = 'btn btn-danger w-100 mt-4';
    resetBtn.textContent = 'إعادة تعيين كاملة';
    resetBtn.addEventListener('click', () => {
        if (confirm('هل أنت متأكد أنك تريد حذف جميع البيانات؟ لا يمكن التراجع عن هذه العملية.')) {
            localStorage.clear();
            // Reset in-memory data
            products = [];
            categories = ['مواد غذائية', 'مشروبات', 'منظفات', 'أدوات مكتبية'];
            sales = [];
            customers = [];
            expenses = [];
            users = [
                { id: 1, username: 'admin', password: 'admin123', type: 'admin', name: 'المدير العام' },
                { id: 2, username: 'branch_manager', password: 'manager123', type: 'branch_manager', name: 'مسؤول الفرع' }
            ];
            currentUser = null;
            currency = 'دينار';
            
            alert('تم حذف جميع البيانات بنجاح. سيتم إعادة تحميل الصفحة.');
            location.reload();
        }
    });
    
    // Add reset section to settings
    const resetSection = document.createElement('div');
    resetSection.className = 'mt-4';
    resetSection.innerHTML = `
        <h3 class="mb-3">إعادة تعيين النظام</h3>
        <p class="text-danger">تحذير: هذه العملية ستحذف جميع البيانات ولا يمكن التراجع عنها!</p>
    `;
    resetSection.appendChild(resetBtn);
    
    // Add to settings form container
    const formContainer = document.querySelector('#settings-form').parentElement;
    formContainer.appendChild(resetSection);
}

// Load cart state from localStorage
function loadCartState() {
    const savedSales = localStorage.getItem('sales');
    if (savedSales) {
        sales = JSON.parse(savedSales);
    }
}

// Call loadCartState on initial load
loadCartState();

// Initialize the app
document.addEventListener('DOMContentLoaded', () => {
    // Check if we're already logged in (for page refresh)
    if (localStorage.getItem('loggedIn') === 'true' && currentUser) {
        loginScreen.style.display = 'none';
        mainApp.style.display = 'block';
        updateUserInfo();
        updateNavigationForUser();
        loadTab('pos');
    }

    // Initialize category filter if exists
    if (document.getElementById('category-filter')) {
        document.getElementById('category-filter').addEventListener('change', () => {
            document.getElementById('product-grid').innerHTML = renderProductsForPOS();
        });
    }
});