# تحديثات نظام نقاط البيع

## التحديثات الجديدة

### 1. نظام المستخدمين المتعدد
- تم إضافة نظام مستخدمين متعدد بدلاً من كلمة مرور واحدة
- يدعم النظام الآن نوعين من المستخدمين:
  - **مدير عادي (Admin)**: له صلاحيات إدارة المنتجات والمبيعات والتقارير
  - **مسؤول الفرع (Branch Manager)**: له جميع صلاحيات المدير + إدارة المستخدمين

### 2. شاشة تسجيل الدخول المحدثة
- إضافة حقل اسم المستخدم
- عرض الحسابات الافتراضية للمساعدة

### 3. إدارة المستخدمين (مسؤول الفرع فقط)
- إضافة مستخدمين جدد
- تعديل بيانات المستخدمين الموجودين
- حذف المستخدمين
- البحث في قائمة المستخدمين
- عرض نوع كل مستخدم

### 4. تحسينات واجهة المستخدم
- عرض معلومات المستخدم الحالي في الرأس
- إظهار نوع المستخدم بألوان مختلفة
- تحديث التنقل حسب نوع المستخدم

## الحسابات الافتراضية

### مدير عادي
- **اسم المستخدم**: admin
- **كلمة المرور**: admin123
- **الصلاحيات**: إدارة المنتجات، المبيعات، العملاء، التقارير، المصروفات، الإعدادات

### مسؤول الفرع
- **اسم المستخدم**: branch_manager
- **كلمة المرور**: manager123
- **الصلاحيات**: جميع صلاحيات المدير + إدارة المستخدمين

## كيفية استخدام النظام الجديد

### تسجيل الدخول
1. أدخل اسم المستخدم
2. أدخل كلمة المرور
3. اضغط دخول

### إدارة المستخدمين (مسؤول الفرع فقط)
1. انتقل إلى تبويب "إدارة المستخدمين"
2. لإضافة مستخدم جديد:
   - املأ النموذج في الجانب الأيسر
   - اختر نوع المستخدم
   - اضغط "إضافة مستخدم"
3. لتعديل مستخدم:
   - اضغط "تعديل" بجانب المستخدم المطلوب
   - عدل البيانات في النافذة المنبثقة
   - اضغط "حفظ التغييرات"
4. لحذف مستخدم:
   - اضغط "حذف" بجانب المستخدم المطلوب
   - أكد الحذف

### البحث عن المستخدمين
- استخدم مربع البحث في أعلى قائمة المستخدمين
- يمكن البحث بالاسم أو اسم المستخدم

## ملاحظات مهمة

1. **الأمان**: تأكد من تغيير كلمات المرور الافتراضية
2. **النسخ الاحتياطي**: يتم حفظ جميع البيانات في localStorage
3. **إعادة التعيين**: تؤدي إعادة التعيين إلى حذف جميع المستخدمين وإعادة إنشاء الحسابات الافتراضية
4. **الصلاحيات**: لا يمكن للمستخدم حذف حسابه الخاص

## التحسينات المستقبلية المقترحة

1. إضافة تشفير لكلمات المرور
2. إضافة سجل العمليات (Audit Log)
3. إضافة صلاحيات أكثر تفصيلاً
4. إضافة إعدادات الجلسة وانتهاء الصلاحية
5. إضافة نظام استرداد كلمة المرور

---

**تم التطوير بواسطة**: Augment Agent
**تاريخ التحديث**: ديسمبر 2024
