{"extensions": {"settings": {"dgiklkfkllikcanfonkcabmbdfmgleag": {"account_extension_type": 0, "active_permissions": {"api": [], "explicit_host": [], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_capabilities": {"include_globs": ["https://*excel.officeapps.live.com/*", "https://*onenote.officeapps.live.com/*", "https://*powerpoint.officeapps.live.com/*", "https://*word-edit.officeapps.live.com/*", "https://*excel.officeapps.live.com.mcas.ms/*", "https://*onenote.officeapps.live.com.mcas.ms/*", "https://*word-edit.officeapps.live.com.mcas.ms/*", "https://*excel.partner.officewebapps.cn/*", "https://*onenote.partner.officewebapps.cn/*", "https://*powerpoint.partner.officewebapps.cn/*", "https://*word-edit.partner.officewebapps.cn/*", "https://*excel.gov.online.office365.us/*", "https://*onenote.gov.online.office365.us/*", "https://*powerpoint.gov.online.office365.us/*", "https://*word-edit.gov.online.office365.us/*", "https://*excel.dod.online.office365.us/*", "https://*onenote.dod.online.office365.us/*", "https://*powerpoint.dod.online.office365.us/*", "https://*word-edit.dod.online.office365.us/*", "https://*visio.partner.officewebapps.cn/*", "https://*visio.gov.online.office365.us/*", "https://*visio.dod.online.office365.us/*"], "matches": ["https://*.officeapps.live.com/*", "https://*.officeapps.live.com.mcas.ms/*", "https://*.partner.officewebapps.cn/*", "https://*.gov.online.office365.us/*", "https://*.dod.online.office365.us/*", "https://*.app.whiteboard.microsoft.com/*", "https://*.whiteboard.office.com/*", "https://*.app.int.whiteboard.microsoft.com/*", "https://*.whiteboard.office365.us/*", "https://*.dev.whiteboard.microsoft.com/*"], "permissions": ["clipboardRead", "clipboardWrite"]}, "default_locale": "en", "description": "This extension grants Microsoft web sites permission to read and write from the clipboard.", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQCz4t/X7GeuP6GBpjmxndrjtzF//4CWeHlC68rkoV7hP3h5Ka6eX7ZMNlYJkSjmB5iRmPHO5kR1y7rGY8JXnRPDQh/CQNLVA7OsKeV6w+UO+vx8KGI+TrTAhzH8YGcMIsxsUjxtC4cBmprja+xDr0zVp2EMgqHu+GBKgwSRHTkDuwIDAQAB", "manifest_version": 2, "minimum_chrome_version": "77", "name": "Microsoft Clipboard Extension", "version": "1.0"}, "path": "C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\136.0.3240.92\\resources\\edge_clipboard", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}, "mhjfbmdgcfjbbpaeojofohoefgiehjai": {"account_extension_type": 0, "active_permissions": {"api": ["contentSettings", "fileSystem", "fileSystem.write", "metricsPrivate", "tabs", "resourcesPrivate", "pdfViewerPrivate", "fileSystem.readFullPath", "errorReporting", "edgeLearningToolsPrivate", "fileSystem.getCurrentEntry", "edgePdfPrivate", "edgeCertVerifierPrivate"], "explicit_host": ["edge://resources/*", "edge://webui-test/*"], "manifest_permissions": [], "scriptable_host": []}, "commands": {}, "content_settings": [], "creation_flags": 1, "disable_reasons": [], "events": [], "first_install_time": "*****************", "from_webstore": false, "incognito_content_settings": [], "incognito_preferences": {}, "last_update_time": "*****************", "location": 5, "manifest": {"content_security_policy": "script-src 'self' 'wasm-eval' blob: filesystem: chrome://resources chrome://webui-test; object-src * blob: externalfile: file: filesystem: data:; trusted-types edge-internal fast-html pdf-url edge-pdf-static-policy;", "description": "", "incognito": "split", "key": "MIGfMA0GCSqGSIb3DQEBAQUAA4GNADCBiQKBgQDN6hM0rsDYGbzQPQfOygqlRtQgKUXMfnSjhIBL7LnReAVBEd7ZmKtyN2qmSasMl4HZpMhVe2rPWVVwBDl6iyNE/Kok6E6v6V3vCLGsOpQAuuNVye/3QxzIldzG/jQAdWZiyXReRVapOhZtLjGfywCvlWq7Sl/e3sbc0vWybSDI2QIDAQAB", "manifest_version": 2, "mime_types": ["application/pdf"], "mime_types_handler": "edge_pdf/index.html", "name": "Microsoft Edge PDF Viewer", "offline_enabled": true, "permissions": ["errorReporting", "chrome://resources/", "chrome://webui-test/", "contentSettings", "metricsPrivate", "edgeCertVerifierPrivate", "edgeLearningToolsPrivate", "edgePdfPrivate", "pdfViewerPrivate", "resourcesPrivate", "tabs", {"fileSystem": ["write", "<PERSON><PERSON><PERSON><PERSON><PERSON>", "getCurrentEntry"]}], "version": "1"}, "path": "C:\\Program Files (x86)\\Microsoft\\EdgeWebView\\Application\\136.0.3240.92\\resources\\edge_pdf", "preferences": {}, "regular_only_preferences": {}, "was_installed_by_default": false, "was_installed_by_oem": false}}}, "protection": {"macs": {"browser": {"show_home_button": "C4E7EEA7B8E448ECB86D5AAC9E777F7AB1C2B8A0E5C27BECBFC7424755F53841"}, "default_search_provider_data": {"template_url_data": "527A5E90ABB595C9C2E22FC1EB47CB0434AA4C835E686E021F25BE61B93B0B90"}, "edge": {"services": {"account_id": "ABAB6859E53AFCD4BB47471BF882CEF5D2CE33A8B7A727863380CC10087C50B2", "last_username": "125374E2E87F55AFF9D35136924FC074B7FFBC66772A1F63D1E0C18EBDB43755"}}, "enterprise_signin": {"policy_recovery_token": "61C878A3D810C51A435E03032F78D8927E372E6C6262F21006FE01B1A70769AD"}, "extensions": {"settings": {"dgiklkfkllikcanfonkcabmbdfmgleag": "F5CFD3F93F8F01D3141A8E5772884FC5DF203DDFF95EB85FE4C1AECEDF01E91B", "mhjfbmdgcfjbbpaeojofohoefgiehjai": "0C72663886CEB81E04EAEE1D4760B98005CC97E422E7968ABA2AEF7324D15955"}, "ui": {"developer_mode": "2380ADAA8FDA0DC4903121156CB81209B549F0786CB3F6630856E24FF6005647"}}, "google": {"services": {"last_signed_in_username": "2FC12BC242121E767640E1A4E7C7DDF3CB551EFF9D31DD31C152C81472540828"}}, "homepage": "B88D42B2A7682033D012CA4E683FC5953021ABC76BEE76A7C0E44C138FA7A4F9", "homepage_is_newtabpage": "72A30E2D02E62A0DCDB99FD0ECB3E3377E55344057B1A90D59AEA62D4C6AAE64", "media": {"cdm": {"origin_data": "89CE2A27BBD278C0A07E0E72F4C855D683C58D8A06F11BDA573322C2A5CC4ECB"}, "storage_id_salt": "BE817BF0ED90C033C4E78A3DDFFAA7A69857386937F098AB2C7047FDD589254D"}, "pinned_tabs": "5E2BD87213FB9D428C03D57CADFF80D641F0CB498C89841206F40F89C5E76EDA", "prefs": {"preference_reset_time": "3481F6C328FAAA41747AF169257C496C55B2CEA5C37F6590D74084D4F970F49B"}, "safebrowsing": {"incidents_sent": "33DD8E1C4DEADBA4E4956D5A70EE806C49E98CBEA8CE096B942090DB6FB129B6"}, "search_provider_overrides": "4BA7C15AFB7154E7CCA313C9B76F6C3CFFE46AC995297450F05AB15B9E5A76E4", "session": {"restore_on_startup": "5BD655CD37C2E680F20FAFE43AE552C1B2BDC49DF77FC5F1EA64CD0EB42922B2", "startup_urls": "A16E25BFEB07DEC5753FD502A38352F244D1531CE531764DAE594FFE4597B0A8"}}, "super_mac": "F5FBE63BFCC348EAF8347DFD8296FED47C49AE4C02D0179EEED54BC2527355D9"}}