/* Base Neumorphic Styling */
body {
    font-family: '<PERSON><PERSON><PERSON>', 'Segoe UI', sans-serif;
    background: #e0e5ec;
    color: #4a5568;
    line-height: 1.7;
    min-height: 100vh;
    padding: 20px;
}

/* Neumorphic Container */
.neumorphic {
    background: #e0e5ec;
    border-radius: 20px;
    box-shadow: 
        10px 10px 20px #c1c8d3, 
        -10px -10px 20px #ffffff;
    padding: 25px;
    transition: all 0.4s ease;
}

.neumorphic:hover {
    box-shadow: 
        8px 8px 16px #c1c8d3, 
        -8px -8px 16px #ffffff;
}

/* Login Screen */
#login-screen {
    display: flex;
    align-items: center;
    justify-content: center;
    min-height: 100vh;
    background: linear-gradient(135deg, #2c3e50, #1a1a2e);
    padding: 20px;
}

.login-card {
    background: #e0e5ec;
    border-radius: 30px;
    box-shadow: 
        15px 15px 30px #0a0e15, 
        -15px -15px 30px #2a3a4b;
    padding: 40px;
    max-width: 500px;
    width: 100%;
}

/* Header & Navigation */
header {
    background: #e0e5ec;
    border-radius: 20px;
    box-shadow: 
        8px 8px 16px #c1c8d3, 
        -8px -8px 16px #ffffff;
    padding: 20px 30px;
    margin-bottom: 30px;
    display: flex;
    justify-content: space-between;
    align-items: center;
}

.nav-tabs {
    background: #e0e5ec;
    border-radius: 15px;
    box-shadow: 
        inset 5px 5px 10px #c1c8d3, 
        inset -5px -5px 10px #ffffff;
    padding: 8px;
    margin-bottom: 30px;
}

.nav-tabs .nav-link {
    font-weight: 700;
    color: #4a5568;
    border: none;
    border-radius: 12px;
    padding: 12px 25px;
    transition: all 0.3s;
    margin: 0 5px;
}

.nav-tabs .nav-link.active {
    background: linear-gradient(145deg, #d0d7e2, #f0f5ff);
    box-shadow: 
        5px 5px 10px #c1c8d3, 
        -5px -5px 10px #ffffff;
    color: #3498db;
}

/* Cards */
.card {
    background: #e0e5ec;
    border: none;
    border-radius: 25px;
    box-shadow: 
        10px 10px 20px #c1c8d3, 
        -10px -10px 20px #ffffff;
    margin-bottom: 30px;
    transition: all 0.4s ease;
    overflow: hidden;
}

.card:hover {
    transform: translateY(-5px);
    box-shadow: 
        15px 15px 30px #c1c8d3, 
        -15px -15px 30px #ffffff;
}

.card-header {
    background: linear-gradient(145deg, #d0d7e2, #f0f5ff);
    border: none;
    padding: 20px 25px;
    border-radius: 25px 25px 0 0 !important;
}

/* Buttons */
.btn {
    font-weight: 700;
    border-radius: 15px;
    padding: 12px 25px;
    transition: all 0.3s;
    border: none;
    position: relative;
    overflow: hidden;
    z-index: 1;
}

.btn:before {
    content: '';
    position: absolute;
    top: 0;
    left: 0;
    width: 0%;
    height: 100%;
    background: rgba(255,255,255,0.2);
    transition: all 0.4s;
    z-index: -1;
}

.btn:hover:before {
    width: 100%;
}

.btn-primary {
    background: linear-gradient(145deg, #3498db, #2980b9);
    box-shadow: 5px 5px 10px #c1c8d3, -5px -5px 10px #ffffff;
    color: white;
}

.btn-danger {
    background: linear-gradient(145deg, #e74c3c, #c0392b);
    box-shadow: 5px 5px 10px #c1c8d3, -5px -5px 10px #ffffff;
    color: white;
}

.btn-success {
    background: linear-gradient(145deg, #2ecc71, #27ae60);
    box-shadow: 5px 5px 10px #c1c8d3, -5px -5px 10px #ffffff;
    color: white;
}

.btn-warning {
    background: linear-gradient(145deg, #f39c12, #d35400);
    box-shadow: 5px 5px 10px #c1c8d3, -5px -5px 10px #ffffff;
    color: white;
}

/* Product Cards */
.product-card {
    background: #e0e5ec;
    border-radius: 20px;
    box-shadow: 
        8px 8px 16px #c1c8d3, 
        -8px -8px 16px #ffffff;
    padding: 20px;
    text-align: center;
    cursor: pointer;
    transition: all 0.4s;
    position: relative;
    overflow: hidden;
}

.product-card:hover {
    transform: translateY(-8px);
    box-shadow: 
        12px 12px 24px #c1c8d3, 
        -12px -12px 24px #ffffff;
}

.product-card:active {
    box-shadow: 
        inset 5px 5px 10px #c1c8d3, 
        inset -5px -5px 10px #ffffff;
}

/* Forms */
.form-control, .form-select {
    background: #e0e5ec;
    border: none;
    border-radius: 15px;
    box-shadow: 
        inset 5px 5px 10px #c1c8d3, 
        inset -5px -5px 10px #ffffff;
    padding: 12px 20px;
    transition: all 0.3s;
}

.form-control:focus, .form-select:focus {
    box-shadow: 
        inset 2px 2px 5px #c1c8d3, 
        inset -2px -2px 5px #ffffff;
    background: #e0e5ec;
}

/* Tables */
.table {
    background: #e0e5ec;
    border-radius: 20px;
    box-shadow: 
        8px 8px 16px #c1c8d3, 
        -8px -8px 16px #ffffff;
    overflow: hidden;
}

.table thead th {
    background: linear-gradient(145deg, #d0d7e2, #f0f5ff);
    color: #2c3e50;
    font-weight: 700;
    padding: 18px;
    border: none;
}

.table tbody tr {
    transition: all 0.3s;
}

.table tbody tr:hover {
    background: rgba(208, 215, 226, 0.3);
}

/* Animations */
@keyframes fadeIn {
    from { opacity: 0; transform: translateY(15px); }
    to { opacity: 1; transform: translateY(0); }
}

@keyframes pulse {
    0% { transform: scale(1); }
    50% { transform: scale(1.03); }
    100% { transform: scale(1); }
}

.card, .product-card, .table {
    animation: fadeIn 0.6s ease-out;
}

.btn {
    animation: fadeIn 0.8s ease-out;
}

/* Button Hover Effects */
.btn:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 
        8px 8px 16px #c1c8d3, 
        -8px -8px 16px #ffffff;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav-tabs .nav-link {
        padding: 10px 15px;
        font-size: 0.9rem;
        margin: 5px;
    }
    
    header {
        flex-direction: column;
        gap: 15px;
        text-align: center;
    }
    
    .product-grid {
        grid-template-columns: repeat(auto-fill, minmax(150px, 1fr));
    }
}

/* Icons */
.btn i {
    margin-left: 8px;
}

/* Utility Classes */
.text-center {
    text-align: center;
}

.mb-4 {
    margin-bottom: 1.5rem !important;
}

.p-4 {
    padding: 1.5rem !important;
}

/* Print Styles */
@media print {
    .no-print {
        display: none;
    }
    
    body {
        background: white;
        padding: 0;
    }
    
    .card, .table {
        box-shadow: none;
        background: white;
    }
}
/* Out of stock styles */
.product-card.out-of-stock {
    opacity: 0.7;
    cursor: not-allowed;
    background: #f5f5f5;
}

.product-card.out-of-stock:hover {
    transform: none;
    box-shadow: 
        8px 8px 16px #c1c8d3, 
        -8px -8px 16px #ffffff;
}

.out-of-stock-alert {
    color: #e74c3c;
    font-weight: bold;
    margin-top: 8px;
}
/* Product name button styles */
.product-name-btn {
    width: 100%;
    padding: 12px 25px;
    margin-bottom: 10px;
    text-align: center;
    white-space: normal;
    word-break: break-word;
    font-weight: bold;
    font-size: 1.1rem;
    border-radius: 15px;
    background: linear-gradient(145deg, #3498db, #2980b9);
    color: white;
    border: none;
    transition: all 0.3s;
}

.product-name-btn:hover {
    transform: translateY(-3px) scale(1.02);
    box-shadow: 8px 8px 16px #c1c8d3, -8px -8px 16px #ffffff;
}

.product-name-btn:active {
    box-shadow: inset 5px 5px 10px #c1c8d3, inset -5px -5px 10px #ffffff;
}
/* Fixed cart styles */
.fixed-cart {
    position: fixed;
    top: 50%;
    left: 20px;
    transform: translateY(-50%);
    width: 350px;
    max-width: 30%;
    z-index: 1000;
    box-shadow: 0 0 20px rgba(0,0,0,0.2);
    border-radius: 25px;
    background: rgba(255, 255, 255, 0.9);
    padding: 15px;
    margin-right: 20px;
    display: flex;
    flex-direction: column;
    max-height: 90vh; /* Limit height to viewport */
}

/* Adjust product grid to prevent overlap */
#product-grid {
    margin-left: 380px; /* Prevent content overlap */
    padding-bottom: 0;
}

#cart {
    max-height: 50vh; /* Make cart items scrollable */
    overflow-y: auto;
}

.checkout-section {
    position: sticky;
    bottom: 0;
    background: rgba(255, 255, 255, 0.95);
    padding: 15px;
    border-top: 1px solid #eee;
    z-index: 100;
}

/* Responsive design for smaller screens */
@media (max-width: 1200px) {
    .fixed-cart {
        width: 300px;
        max-width: 35%;
    }
    #product-grid {
        margin-left: 330px;
    }
}

@media (max-width: 992px) {
    .fixed-cart {
        width: 250px;
        max-width: 40%;
    }
    #product-grid {
        margin-left: 280px;
    }
}

@media (max-width: 768px) {
    .fixed-cart {
        position: relative;
        top: auto;
        left: auto;
        transform: none;
        width: 100%;
        max-width: 100%;
        margin: 20px 0;
    }
    #product-grid {
        margin-left: 0;
    }
}